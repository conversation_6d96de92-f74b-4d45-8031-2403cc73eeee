import React, { useEffect, useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { DateTime } from 'luxon';
import { Tag } from 'primereact/tag';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import moment from 'moment';
import APIServices from '../../../../service/APIService';
import { API } from '../../../../constants/api_url';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { FilterService } from 'primereact/api';


const DealersTable = ({ data, dealerList, assessorList, globalFilter, editDealer }) => {
    const [dealerSelfSubmissions, setDealerSelfSubmissions] = useState([]);
    const [search, setSearch] = useState('');
    const [dateFilter, setDateFilter] = useState({ start: null, end: null });
    const [filteredData, setFilteredData] = useState([]);
    FilterService.register('custom_assessor', (value, filterValues) => {
        if (!filterValues || filterValues.length === 0) return true;
        if (!Array.isArray(value)) return false;
        return value.some((id) => filterValues.includes(id));
    });
    const [filters, setFilters] = useState({
        'vendor.code': { matchMode: 'in', value: null },
        'vendor.dealerName': { matchMode: 'in', value: null },
        'vendor.dealerLocation': { matchMode: 'in', value: null },
        'vendor.dealerZone': { matchMode: 'in', value: null },
        'vendor.dealerCategory': { matchMode: 'in', value: null },
        'latestSubmission': { matchMode: 'in', value: null },
        assessors: { value: null, matchMode: 'custom_assessor' },
    });
    useEffect(() => {
        const fetchSubmissions = async () => {
            try {
                const res = await APIServices.get(API.DealerSelfSubmission);
                setDealerSelfSubmissions(res?.data || []);
            } catch (error) {
                console.error('Error fetching DealerSelfSubmission:', error);
            }
        };
        fetchSubmissions();
    }, []);

    // Define applyFilters function before using it in useEffect
    const applyFilters = (dataToFilter, searchValue = search) => {
        // Apply search filter
        let filteredData = dataToFilter || [];
        if (searchValue) {
            filteredData = filteredData.filter(x =>
                x?.vendor?.dealerName?.trim().toLowerCase().includes(searchValue?.trim().toLowerCase()) ||
                x?.vendor?.code?.trim().toLowerCase().includes(searchValue?.trim().toLowerCase())
            );
        }

        // Apply date range filter
        if (dateFilter.start && dateFilter.end) {
            filteredData = filteredData.filter(rowData => {
                const dateStr = rowData?.auditStartDate;
                if (!dateStr) return true;

                const itemDate = moment(dateStr).toDate();
                const startDate = moment(dateFilter.start).startOf('day').toDate();
                const endDate = moment(dateFilter.end).endOf('day').toDate();

                return itemDate >= startDate && itemDate <= endDate;
            });
        }

        // Add tableIndex property for sorting
        const indexedData = filteredData.map((item, index) => ({
            ...item,
            tableIndex: index + 1
        }));

        setFilteredData(indexedData);
    };

    useEffect(() => {
        applyFilters(data);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [data]);

    useEffect(() => {
        applyFilters(data);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dateFilter]);

      const zonalOfficeList = [{ name: "Central", value: 1 }, { name: "East", value: 2 }, { name: "North", value: 3 }, { name: "South", value: 9 }, { name: "South1", value: 4 }, { name: "South2", value: 5 }, { name: "West", value: 8 }, { name: "West1", value: 6 }, { name: "West2", value: 7 }, { name: "TN", value: 10 }, { name: "North1", value: 11 }, { name: "North2", value: 12 }]

    const dealerType = [
        { name: 'Authorized Main Dealer', value: 1 },
        { name: 'Authorized Dealer', value: 2 },
        { name: 'Authorized Parts Stockist (APS)', value: 3 },
        { name: 'Area Office', value: 4 }
    ];

    const RowFilterTemplate = (options, obj) => {
        // Extract unique values for the dropdown
        const uniqueValues = [...new Set(data.map((i) => {
            // Handle nested properties
            if (obj.includes('.')) {
                const parts = obj.split('.');
                let value = i;
                for (const part of parts) {
                    value = value?.[part];
                }
                return value;
            }
            return i[obj];
        }))].filter(x => x);

        // Format the options for the MultiSelect
        let selectOptions;

        // Special handling for category and zone which are numeric IDs
        if (obj === 'vendor.dealerCategory') {
            selectOptions = uniqueValues.map(val => ({
                label: dealerType.find(x => x.value === val)?.name || val?.toString() || '',
                value: val
            }));
        } else if (obj === 'vendor.dealerZone') {
            selectOptions = uniqueValues.map(val => ({
                label: zonalOfficeList.find(x => x.value === val)?.name || val?.toString() || '',
                value: val
            }));
        } else {
            selectOptions = uniqueValues.map(val => ({
                label: val?.toString() || '',
                value: val
            }));
        }

        return (
            <MultiSelect
                value={options.value}
                options={selectOptions}
                onChange={(e) => options.filterCallback(e.value)}
                optionLabel="label"
                placeholder="Select"
                filter
                panelClassName='hidefilter'
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: '12rem' }}
            />
        );
    };
    const RowFilterTemplate_ = (options, obj) => {
        // Extract unique values for the dropdown
        const uniqueValues = [...new Set(filteredData.map(x => ({ ...x, latestSubmission: getLastSubmissionMonth(x) })).map((i) => {

            return i[obj];
        }))].filter(x => x);
        // Format the options for the MultiSelect
        let selectOptions = uniqueValues.map(val => ({
            label: val?.toString() || '',
            value: val
        }));


        return (
            <MultiSelect
                value={options.value}
                options={selectOptions}
                onChange={(e) => options.filterCallback(e.value)}
                optionLabel="label"
                placeholder="Select"
                filter
                panelClassName='hidefilter'
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: '12rem' }}
            />
        );
    };

    // Sort function for S.No column
    const sortIndexColumn = (e) => {
        const { data, order } = e;

        // Create a new array with the current data and add an index property
        const indexedData = data.map((item, index) => ({
            ...item,
            tableIndex: index + 1
        }));

        // Sort based on the index
        if (order === 1) { // ascending
            return indexedData.sort((a, b) => a.tableIndex - b.tableIndex);
        } else { // descending
            return indexedData.sort((a, b) => b.tableIndex - a.tableIndex);
        }
    };

    // Sort function for self assessment score
    const sortSelfAssessmentScore = (e) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const scoreA = getSelfAssessmentScore(a);
                const scoreB = getSelfAssessmentScore(b);

                if (scoreA === 'NA' && scoreB === 'NA') return 0;
                if (scoreA === 'NA') return 1; // 'NA' values at the end for ascending
                if (scoreB === 'NA') return -1;

                return scoreA - scoreB;
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const scoreA = getSelfAssessmentScore(a);
                const scoreB = getSelfAssessmentScore(b);

                if (scoreA === 'NA' && scoreB === 'NA') return 0;
                if (scoreA === 'NA') return 1; // 'NA' values at the end for descending too
                if (scoreB === 'NA') return -1;

                return scoreB - scoreA;
            });
        }
    };

    const getSelfAssessmentScore = (rowData) => {
        const matched = dealerSelfSubmissions
            .filter(sub => sub.dealerId === rowData.dealerId)
            .sort((a, b) => {
                const dateA = DateTime.fromFormat(a.reporting_period?.[0] || '', 'MM-yyyy');
                const dateB = DateTime.fromFormat(b.reporting_period?.[0] || '', 'MM-yyyy');
                return dateB.toMillis() - dateA.toMillis();
            });

        if (matched.length > 0) {
            try {
                const scoreObj = JSON.parse(matched[0].score || '{}');
                return scoreObj.overallScore ?? 'NA';
            } catch {
                return 'NA';
            }
        }

        return 'NA';
    };

    const calibrationIdBodyTemplate = (rowData) => (
        <span className="text-primary cursor-pointer" style={{ textDecoration: 'underline' }}>
            {'MSI-' + (rowData?.vendor?.code || 'NA') + '-' + DateTime.fromISO(rowData.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}
        </span>
    );

    const nameTemplate = (rowData) => rowData?.vendor?.dealerName || 'NA';
    const locationTemplate = (rowData) => rowData.vendor?.dealerLocation || 'NA';
    const zoneTemplate = (rowData) => zonalOfficeList.find(x => x.value === rowData?.vendor?.dealerZone)?.name || 'NA';
    const categoryTemplate = (rowData) => dealerType.find(x => x.value === rowData?.vendor?.dealerCategory)?.name || 'NA';

    const calibiratorTemplate = (rowData) => {
        if (rowData?.assessors?.length) {
            return rowData.assessors
                .map(id => assessorList.find(i => i.id === id))
                .filter(Boolean)
                .map(i => i?.information?.empname || '')
                .join(', ');
        }
        return 'Not Assigned';
    };

    const selfAssessmentMonthTemplate = (rowData) => {
        console.log(rowData)
        const matched = dealerSelfSubmissions
            .filter(sub => sub.dealerId === rowData.dealerId)
            .sort((a, b) => moment(b.reporting_period?.[0], 'MM-YYYY') - moment(a.reporting_period?.[0], 'MM-YYYY'));

        if (!matched.length || !matched[0]?.reporting_period?.[0]) return 'NA';
        return moment(matched[0].reporting_period[0], 'MM-YYYY').format('MMMM YYYY');
    };

    const selfAssessmentScoreTemplate = (rowData) => {
        const matched = dealerSelfSubmissions
            .filter(sub => sub.dealerId === rowData.dealerId)
            .sort((a, b) => {
                const dateA = DateTime.fromFormat(a.reporting_period?.[0] || '', 'MM-yyyy');
                const dateB = DateTime.fromFormat(b.reporting_period?.[0] || '', 'MM-yyyy');
                return dateB.toMillis() - dateA.toMillis();
            });

        if (matched.length > 0) {
            try {
                const scoreObj = JSON.parse(matched[0].score || '{}');
                return scoreObj.overallScore ?? 'NA';
            } catch {
                return 'NA';
            }
        }

        return 'NA';
    };

    const calibirationDateTemplate = (rowData) =>
        DateTime.fromISO(rowData.auditStartDate, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy');

    const actionBodyTemplate = (rowData) => (
        <button className="btn btn-sm btn-primary" onClick={() => editDealer(rowData)}>
            <i className="pi pi-pencil" />
        </button>
    );

    const clearDateFilter = () => {
        setDateFilter({ start: null, end: null });
    };

    const handleSearchChange = (e) => {
        setSearch(e.target.value);
        applyFilters(data, e.target.value);
    };
    const exportExcel = () => {
        if (!filteredData || filteredData.length === 0) {
            alert('No data to export.');
            return;
        }

        const exportData = filteredData.map((item) => ({
            'S.No': item.tableIndex || '',
            'Calibration ID': item.vendor?.code ? `MSI-${item.vendor.code}-${DateTime.fromISO(item.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}` : 'NA',
            'Dealer Name': item.vendor?.dealerName || 'NA',
            'Location': item.vendor?.dealerLocation || 'NA',
            'Zone': zonalOfficeList.find(x => x.value === item.vendor?.dealerZone)?.name || 'NA',
            'Category': dealerType.find(x => x.value === item.vendor?.dealerCategory)?.name || 'NA',
            'Latest Self-assessment Month': selfAssessmentMonthTemplate(item),
            'MSI Self-assessment Score': selfAssessmentScoreTemplate(item),
            'Planned Calibration Date': item.auditStartDate ? DateTime.fromISO(item.auditStartDate, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy') : 'NA',
            'Calibration Team Member': calibiratorTemplate(item)
        }));

        const worksheet = XLSX.utils.json_to_sheet(exportData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Dealers');

        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        const dataBlob = new Blob([excelBuffer], { type: 'application/octet-stream' });
        saveAs(dataBlob, `Dealers_List_${moment().format('YYYYMMDD_HHmmss')}.xlsx`);
    };
    const getLastSubmissionMonth = (rowData) => {

        console.log(rowData)
        const matched = dealerSelfSubmissions
            .filter(sub => sub.dealerId === rowData.dealerId)
            .sort((a, b) => {
                const dateA = moment(a.reporting_period?.[0], 'MM-YYYY');
                const dateB = moment(b.reporting_period?.[0], 'MM-YYYY');
                return dateB - dateA;
            });
        if (matched.length === 0 || !matched[0]?.reporting_period?.[0]) return '';
        console.log(DateTime.fromFormat(matched[0].reporting_period[0], 'LL-yyyy'))
        return DateTime.fromFormat(matched[0].reporting_period[0], 'LL-yyyy').toFormat('LLLL yyyy');

    };
    const selfAssessmentMonthSort = (e) => {
        console.log(e.data)
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const monthA = a.latestSubmission
                const monthB = b.latestSubmission

                if (!monthA && !monthB) return 0;
                if (!monthA) return 1;
                if (!monthB) return -1;

                const dateA = DateTime.fromFormat(monthA, 'LLLL yyyy')
                const dateB = DateTime.fromFormat(monthB, 'LLLL yyyy')
                console.log(dateA.isValid, dateB.isValid);
                if (dateA < dateB) return -1;
                if (dateA > dateB) return 1;
                return 0;
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const monthA = a.latestSubmission
                const monthB = b.latestSubmission

                if (!monthA && !monthB) return 0;
                if (!monthA) return 1;
                if (!monthB) return -1;


                const dateA = DateTime.fromFormat(monthA, 'LLLL yyyy')
                const dateB = DateTime.fromFormat(monthB, 'LLLL yyyy')

                if (dateA > dateB) return -1;
                if (dateA < dateB) return 1;
                return 0;
            });
        }
    }

    return (
        <>
            <div className="col-12 flex justify-content-between align-items-center mb-3" >
                <div className="col-6 flex gap-3 align-items-center">
                    <div className="flex flex-column">
                        <label className="mb-1">Calibration Date From</label>
                        <Calendar
                            value={dateFilter.start}
                            onChange={(e) => setDateFilter({ ...dateFilter, start: e.value })}
                            placeholder="Start Date"
                            dateFormat="dd-mm-yy"
                            showIcon
                        />
                    </div>
                    <div className="flex flex-column">
                        <label className="mb-1">To</label>
                        <Calendar
                            value={dateFilter.end}
                            onChange={(e) => setDateFilter({ ...dateFilter, end: e.value })}
                            placeholder="End Date"
                            dateFormat="dd-mm-yy"
                            showIcon
                            minDate={dateFilter.start}
                            disabled={!dateFilter.start}
                        />
                    </div>
                    {(dateFilter.start || dateFilter.end) && (
                        <button
                            className="btn btn-sm btn-outline-secondary align-self-end mb-1"
                            onClick={clearDateFilter}
                            style={{ height: '36px' }}
                        >
                            Clear
                        </button>
                    )}
                </div>
                <div className='col-5'>
                    <span className="p-input-icon-left" style={{ width: '100%' }}>
                        <i className="pi pi-search" />
                        <InputText value={search} style={{ width: '100%' }} onChange={handleSearchChange} placeholder="Search Code/Name" />
                    </span>
                </div>
            </div>
            <div className="d-flex justify-content-between align-items-center mb-3">
                <h4>Dealers ({filteredData.length})</h4>
                <button
                    className="btn btn-sm btn-success"
                    onClick={exportExcel}
                >
                    Download Excel
                </button>
            </div>

            <DataTable
                value={filteredData.map(x => ({ ...x, latestSubmission: getLastSubmissionMonth(x) }))}
                paginator
                rows={10}
                scrollable
                scrollHeight="500px"
                filters={filters}
                onFilter={(e) => setFilters(e.filters)}
                className="mt-2 h-500"
            >
                <Column sortable field="tableIndex" header="S.No" body={(rowData, options) => rowData.tableIndex || options.rowIndex + 1} sortFunction={sortIndexColumn} />
                <Column field="vendor.code" header="Calibration ID" body={calibrationIdBodyTemplate} sortable filter showFilterMatchModes={false} filterElement={(options) => RowFilterTemplate(options, 'vendor.code')} />
                <Column field="vendor.dealerName" header="Name" body={nameTemplate} sortable filter showFilterMatchModes={false} filterElement={(options) => RowFilterTemplate(options, 'vendor.dealerName')} />
                <Column field="vendor.dealerLocation" header="Location" body={locationTemplate} sortable filter showFilterMatchModes={false} filterElement={(options) => RowFilterTemplate(options, 'vendor.dealerLocation')} />
                <Column field="vendor.dealerZone" header="Zone" body={zoneTemplate} sortable filter showFilterMatchModes={false} filterElement={(options) => RowFilterTemplate(options, 'vendor.dealerZone')} />
                <Column field="vendor.dealerCategory" header="Category" body={categoryTemplate} sortable filter showFilterMatchModes={false} filterElement={(options) => RowFilterTemplate(options, 'vendor.dealerCategory')} />

                <Column
                    field="latestSubmission"
                    header="Latest Self-assessment Month"
                    body={selfAssessmentMonthTemplate}
                    sortable
                    sortFunction={selfAssessmentMonthSort}
                    showFilterMatchModes={false}
                    filter filterElement={(options) => RowFilterTemplate_(options, 'latestSubmission')}
                />

                <Column
                    field="selfAssessmentScore"
                    header="MSI Self-assessment Score"
                    body={selfAssessmentScoreTemplate}
                    sortable
                    sortFunction={sortSelfAssessmentScore}
                    showFilterMatchModes={false}
                />

                <Column
                    field="auditStartDate"
                    header="Planned Calibration Date"
                    body={calibirationDateTemplate}
                    sortable
                />

                <Column
                    field="assessors"
                    header="Calibration Team Member"
                    body={calibiratorTemplate} // your function to show names instead of IDs
                    sortable
                    filter
                    filterMatchMode="custom_assessor"
                    showFilterMatchModes={false}
                    showFilterOperator={false}
                    filterElement={(options) => (
                        <MultiSelect
                        panelClassName='hidefilter'
                        filter
                            value={options.value}
                            optionValue="id"
                            optionLabel="label"
                            options={assessorList.filter(i => filteredData.flatMap(x => x?.assessors || []).includes(i.id)).map(x => ({ label: x?.information?.empname || '', id: x.id })).filter(x => x.label)}
                            onChange={(e) => options.filterCallback(e.value)}
                            placeholder="Select Assessor"
                            display="chip"
                            className="p-column-filter"
                        />
                    )}
                />



                <Column header="Schedule MSI Calibration" body={actionBodyTemplate} />
            </DataTable>
        </>
    );
};

export default DealersTable;
