import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ResponsiveContainer,
  CartesianGrid,
  ReferenceLine,
  Label,
  Cell,
} from "recharts";

const ESGGOverviewChart = ({ data }) => {
  // Function to filter data based on category
  const filterDataByCategory = (category) => {
    // Check if data is an array and has elements
    if (!Array.isArray(data) || data.length === 0) {
      console.warn(`No data available or data is not an array`);
      return [];
    }

    // Check if the category field exists in the data
    const hasCategory = data.some(entry => entry.category !== undefined);

    if (!hasCategory) {
      console.warn(`Category field not found in data. First item:`, data[0]);
      // Try to find an alternative way to filter by category
      return data.filter((entry) => {
        // If category field doesn't exist, check if there's a dealerType or similar field
        if (entry.dealerType) {
          return (category === "Sales" && entry.dealerType === 1) ||
                 (category === "Service" && entry.dealerType === 2);
        }
        // If there's a direct category match
        if (entry.category) {
          return entry.category === category;
        }
        return false;
      });
    }

    return data.filter((entry) => entry.category === category);
  };

  // Function to compute total scores
  const computeTotalScore = (filteredData) => {
    if (!filteredData || filteredData.length === 0) {
      console.warn("No data to compute score");
      return 0;
    }

    let totalScore = 0;
    let count = filteredData.length;

    console.log("Computing total score for data:", filteredData);

    filteredData.forEach((entry) => {
      // Check for different possible score fields
      let entryScore = 0;

      // New priority order for score fields:
      // 1. Sum of environment, social, governance, general (as requested)
      // 2. score
      // 3. Category-specific score (sales_score or service_score)
      // 4. total_score

      // Check if all ESGG fields are available
      if (entry.environment !== undefined && entry.social !== undefined && entry.governance !== undefined) {
        // If we have environment, social, governance, and general scores, use their sum
        entryScore = (parseFloat(entry.environment || 0) +
                      parseFloat(entry.social || 0) +
                      parseFloat(entry.governance || 0) +
                      parseFloat(entry.general || 0));
        console.log(`Entry ${entry.branch_code}: Using sum of E+S+G+G: ${entryScore} (${entry.environment} + ${entry.social} + ${entry.governance} + ${entry.general || 0})`);
      } else if (!isNaN(entry.score)) {
        entryScore = parseFloat(entry.score);
        console.log(`Entry ${entry.branch_code}: Using score: ${entryScore}`);
      } else if (entry.category === "Sales" && !isNaN(entry.sales_score)) {
        entryScore = parseFloat(entry.sales_score);
        console.log(`Entry ${entry.branch_code}: Using sales_score: ${entryScore}`);
      } else if (entry.category === "Service" && !isNaN(entry.service_score)) {
        entryScore = parseFloat(entry.service_score);
        console.log(`Entry ${entry.branch_code}: Using service_score: ${entryScore}`);
      } else if (!isNaN(entry.total_score)) {
        entryScore = parseFloat(entry.total_score);
        console.log(`Entry ${entry.branch_code}: Using total_score: ${entryScore}`);
      } else {
        console.warn(`Entry ${entry.branch_code}: No valid score found`);
      }

      totalScore += entryScore;
    });

    const result = totalScore / count;
    console.log(`Total score: ${totalScore}, Count: ${count}, Average: ${result}`);
    return result;
  };

  // Debug the data to see what's coming in
  console.log("Data received in ESGGOverviewChart:", data);

  // Filter data by category and log the results
  const salesData = filterDataByCategory("Sales");
  const serviceData = filterDataByCategory("Service");


  // Aggregating total scores for both Sales and Service
  const salesTotalScore = computeTotalScore(salesData);
  const serviceTotalScore = computeTotalScore(serviceData);



  // Use calculated values from the data
  const salesScore = parseFloat(salesTotalScore.toFixed(1));
  const serviceScore = parseFloat(serviceTotalScore.toFixed(1));
  console.log("Using calculated values - Sales:", salesScore, "Service:", serviceScore);

  // Calculating MSI Score and determining grade - ensure precision
  const msiScore = parseFloat(((salesScore + serviceScore) / 2).toFixed(1));
  console.log("Calculated MSI Score:", msiScore);

  console.log("Formatted scores - Sales:", salesScore, "Service:", serviceScore, "Average:", msiScore);

  const getMSIGrade = (score) => {
    if (score >= 86) return "Platinum";
    if (score >= 71) return "Gold";
    if (score >= 56) return "Silver";
    return "Not Met";
  };

  const msiGrade = getMSIGrade(msiScore);

  // Helper function to clamp values between 0 and maxValue for graph display
  const clampForGraph = (value) => Math.max(0, value);
  const clampRemaining = (avgValue, maxValue) =>
    Math.max(0, Math.min(maxValue, maxValue - avgValue));

  // Data for MSI Chart - use the precise values with clamping for display
  const msiChartData = [
    {
      category: "Sales",
      achieved: salesScore,
      achievedForGraph: clampForGraph(salesScore),
      remaining: 100 - salesScore,
      remainingToMax: clampRemaining(salesScore, 100),
      actualScore: salesScore, // Store the actual score for tooltip
    },
    {
      category: "Service",
      achieved: serviceScore,
      achievedForGraph: clampForGraph(serviceScore),
      remaining: 100 - serviceScore,
      remainingToMax: clampRemaining(serviceScore, 100),
      actualScore: serviceScore, // Store the actual score for tooltip
    },
  ];

  const CustomLegend = (props) => {
    const { payload } = props;
    return (
      <ul
        style={{
          display: "flex",
          listStyleType: "none",
          justifyContent: "center",
          padding: 0,
          marginTop: "10px",
        }}
      >
        {payload.map((entry, index) => (
          <li
            key={`item-${index}`}
            style={{
              color: entry.color,

              marginRight: "5px",
            }}
          >
            <span
              style={{
                color: entry.color,
                backgroundColor: entry.color,
                marginRight: 4,
                fontSize: "20px",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                display: "inline-block",
              }}
            ></span>
            <span style={{ color: "#555", fontSize: "14px" }}>
              {entry.value}
            </span>
          </li>
        ))}
      </ul>
    );
  };
  // Platinum: "#CCCED5",
  // Gold: "#F5C37B",
  // Silver: "#EAECF0",
  // Bronze: "#D28B24",
  // "Needs Improvement": "#EE5724",
  const getBarColor = (score) => {
    if (score === "Platinum") return "#CCCED5"; // Dark Green for Platinum
    if (score === "Gold") return "#F5C37B"; // Green for Gold
    if (score === "Silver") return "#EAECF0"; // Yellow for Silver
    if (score === "Bronze") return "#D28B24"; // Orange for Bronze
    return "#EE5724"; // Red for Needs Improvement
  };

  return (
    <div style={{ padding: "20px" }}>
      {/* MSI Score and Grade Display */}
      <div
        style={{
          display: "flex",
          justifyContent: "space-around",
          marginBottom: "20px",
        }}
      >
        <h2 style={{ color: "#0D5EAF" }}>MSI Score: {msiScore}</h2>
        <h2 style={{ color: "#0D5EAF" }}>MSI Grade: {msiGrade}</h2>
      </div>

      {/* MSI Chart */}
      <div style={{ width: "100%", height: 400, marginBottom: "40px" }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={msiChartData}
            barSize={60}
            margin={{ top: 20, right: 30, left: 20, bottom: 15 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="category" tick={{ fontSize: 12 }} />
            <YAxis domain={[0, 100]} />
            <Tooltip
              formatter={(value, name, props) => {
                const { payload } = props;
                if (name === "Achieved") {
                  // Use the actual score from the data point
                  return [`${payload.achieved.toFixed(1)} (Max: 100)`, name];
                }
                if (name === "Maximum") {
                  return [`Remaining: ${payload.remainingToMax.toFixed(1)}`, name];
                }
                return [value, name];
              }}
            />
            <Legend content={CustomLegend} />
            <Bar dataKey="achievedForGraph" stackId="score" name="Achieved">
              {msiChartData.map((entry, index) => (
                <Cell
                  key={`cell-achieved-${index}`}
                  fill={getBarColor(getMSIGrade(entry.achieved))}
                />
              ))}
            </Bar>
            <Bar
              dataKey="remainingToMax"
              stackId="score"
              fill="#CCCED5"
              name="Maximum"
            />
            {/* Reference Line for MSI Score */}
            <ReferenceLine y={msiScore} stroke="#FF5733" strokeDasharray="3 3">
              <Label
                value={`Average MSI: ${msiScore}`}
                position="center"
                fill="#FF5733"
                fontSize={12}
                fontWeight="bold"
              />
            </ReferenceLine>
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default ESGGOverviewChart;
