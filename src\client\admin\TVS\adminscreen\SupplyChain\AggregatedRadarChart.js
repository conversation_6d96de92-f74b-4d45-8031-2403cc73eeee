import React, { useEffect, useState } from "react";
import {
    RadarChart,
    PolarGrid,
    PolarAngleAxis,
    PolarRadiusAxis,
    Radar,
    Tooltip,
    Legend,
    ResponsiveContainer,
    LabelList,
} from "recharts";


const safeNumber = (value) => {
    const num = parseFloat(value);
    return isNaN(num) ? 0 : num;
};

const aggregateESGData = (dataArray) => {
    if (!dataArray || dataArray.length === 0) return [];

    const totalSuppliers = dataArray.length;

    const maxValues = {
        "Environmental Framework": 40,
        "Sustainability Ambassadorship Framework": 20,
        "Social Stewardship Framework": 10,
        "Occupational Health & Safety Framework": 20,
        "Legal Compliances": 5,
        "Governance Framework": 5,
    };

    const aggregated = {
        "Environmental Framework": 0,
        "Sustainability Ambassadorship Framework": 0,
        "Social Stewardship Framework": 0,
        "Occupational Health & Safety Framework": 0,
        "Legal Compliances": 0,
        "Governance Framework": 0,
    };

    dataArray.forEach(supplier => {
        aggregated["Environmental Framework"] += safeNumber(supplier.environment);
        aggregated["Sustainability Ambassadorship Framework"] += safeNumber(supplier.supplier_sustainability_ambassadorship_framework);
        aggregated["Social Stewardship Framework"] += safeNumber(supplier.social_stewardship_framework);
        aggregated["Occupational Health & Safety Framework"] += safeNumber(supplier.health_safety);
        aggregated["Legal Compliances"] += safeNumber(supplier.legal_compliances);
        aggregated["Governance Framework"] += safeNumber(supplier.governance_framework);
    });

    return Object.entries(aggregated).map(([category, total]) => {
        const averageValue = parseFloat((total / totalSuppliers).toFixed(1));
        const maxValue = maxValues[category] || 1; // Default to 1 if maxValue is not defined

        // Calculate percentage, handle division by zero
        let percentageValue = 0;
        if (maxValue > 0) {
            percentageValue = parseFloat(((averageValue / maxValue) * 100).toFixed(1));
        }

        // Ensure percentage doesn't exceed 100%
        percentageValue = Math.min(percentageValue, 100);

        return {
            category,
            Achieved: averageValue,
            Percentage: percentageValue,
            MaxValue: maxValue
        };
    });
};

const AggregatedRadarChart = ({ filteredSupplyData }) => {
    const [chartData, setChartData] = useState([]);

    useEffect(() => {
        const transformedData = aggregateESGData(filteredSupplyData);
        setChartData(transformedData);
    }, [filteredSupplyData]);

    return (
        <div className="container-fluid mt-4" style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            width: '100%',
            margin: '0 auto'
        }}>
            <h5 className="mb-4 text-center text-dark" style={{
                fontWeight: 'bold',
                fontSize: '18px',
                width: '100%',
                paddingBottom: '10px',
                borderBottom: '2px solid #ddd',
                marginBottom: '20px'
            }}>
                Aggregated ESG Score
            </h5>

            <div style={{ width: '100%', maxWidth: '600px' }}>
                <ResponsiveContainer width="100%" height={400}>
                    <RadarChart data={chartData} cx="50%" cy="50%">
                        <PolarGrid />
                        <PolarAngleAxis dataKey="category" />
                        <PolarRadiusAxis domain={[0, 100]} tickFormatter={(value) => `${value}%`} />
                        <Radar
                            name="Percentage Score"
                            dataKey="Percentage"
                            stroke="#82ca9d"
                            fill="#82ca9d"
                            fillOpacity={0.6}
                        >
                            <LabelList
                                formatter={(value) => `${value}%`}
                                dataKey="Percentage"
                                position="outside"
                                offset={15}
                                style={{ fill: '#444', fontSize: '12px', fontWeight: 'bold' }}
                            />
                        </Radar>
                        <Tooltip
                            formatter={(value, name, props) => {
                                // Add null check for props and props.payload
                                if (!props || !props.payload) {
                                    return [value, name];
                                }

                                if (name === "Percentage Score" &&
                                    props.payload.Achieved !== undefined &&
                                    props.payload.MaxValue !== undefined) {
                                    return [`${value}% (${props.payload.Achieved} / ${props.payload.MaxValue})`, name];
                                }

                                return [value, name];
                            }}
                        />
                        <Legend wrapperStyle={{ position: 'relative', marginTop: '10px', textAlign: 'center' }} />
                    </RadarChart>
                </ResponsiveContainer>
            </div>
        </div>
    );
};

export default AggregatedRadarChart;
